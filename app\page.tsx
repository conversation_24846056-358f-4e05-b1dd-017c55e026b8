"use client";

import Image from "next/image";
import Link from "next/link";
import { useState, useEffect, useRef, useCallback } from "react";
import { Lock, Unlock, Play, Pause, RotateCcw, Menu, X, Home as HomeIcon, BookOpen, Coffee } from "lucide-react";
import {
  trackButtonClick,
  trackRecipeGenerated,
  trackRecipeCompleted,
  trackRecipeShared,
  trackRecipeTimer
} from "@/lib/analytics";

export default function Home() {
  const [cafe, setCafe] = useState<string>("15");
  const [agua, setAgua] = useState<string>("225");
  const [proporcao, setProporcao] = useState<string>("15");
  const [perfilSabor, setPerfilSabor] = useState<string>("Equilibrado");
  const [perfilCorpo, setPerfilCorpo] = useState<string>("Equilibrado");
  const [aguaBloqueada, setAguaBloqueada] = useState<boolean>(true);
  const [proporcaoBloqueada, setProporcaoBloqueada] = useState<boolean>(true);

  // Estados do cronômetro
  const [tempo, setTempo] = useState<number>(0); // em segundos
  const [rodando, setRodando] = useState<boolean>(false);
  const [contandoRegressivo, setContandoRegressivo] = useState<boolean>(false);
  const [contagemRegressiva, setContagemRegressiva] = useState<number>(3);
  const [modalAberto, setModalAberto] = useState<boolean>(false);
  const [sidebarAberta, setSidebarAberta] = useState<boolean>(false);
  const [sidebarFechando, setSidebarFechando] = useState<boolean>(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const regressivaRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const finishAudioRef = useRef<HTMLAudioElement | null>(null);
  const transitionAudioRef = useRef<HTMLAudioElement | null>(null);
  const passoAnteriorRef = useRef<number>(-1);
  const receitaRef = useRef<HTMLDivElement | null>(null);
  const sidebarRef = useRef<HTMLDivElement | null>(null);

  // Funções de armazenamento local
  const salvarPreferencias = useCallback(() => {
    const preferencias = {
      cafe,
      proporcao,
      perfilSabor,
      perfilCorpo,
      aguaBloqueada,
      proporcaoBloqueada
    };
    localStorage.setItem('cereja-preferencias', JSON.stringify(preferencias));
  }, [cafe, proporcao, perfilSabor, perfilCorpo, aguaBloqueada, proporcaoBloqueada]);

  const carregarPreferencias = useCallback(() => {
    try {
      const preferenciasString = localStorage.getItem('cereja-preferencias');
      if (preferenciasString) {
        const preferencias = JSON.parse(preferenciasString);
        setCafe(preferencias.cafe || "15");
        setProporcao(preferencias.proporcao || "15");
        setPerfilSabor(preferencias.perfilSabor || "Equilibrado");
        setPerfilCorpo(preferencias.perfilCorpo || "Equilibrado");
        setAguaBloqueada(preferencias.aguaBloqueada ?? true);
        setProporcaoBloqueada(preferencias.proporcaoBloqueada ?? true);

        // Recalcular água com os valores carregados
        const numCafe = parseFloat(preferencias.cafe || "15");
        const numProporcao = parseFloat(preferencias.proporcao || "15");
        if (numCafe > 0 && numProporcao > 0) {
          setAgua(arredondar(numCafe * numProporcao));
        }
      }
    } catch (error) {
      console.error('Erro ao carregar preferências:', error);
    }
  }, []); // Sem dependências pois só é chamada uma vez na inicialização

  // Carregar preferências ao inicializar
  useEffect(() => {
    carregarPreferencias();
  }, [carregarPreferencias]);

  // Função para fechar sidebar com animação
  const fecharSidebar = useCallback(() => {
    setSidebarFechando(true);
    setTimeout(() => {
      setSidebarAberta(false);
      setSidebarFechando(false);
    }, 300);
  }, []);

  // Fechar sidebar ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        fecharSidebar();
      }
    };

    if (sidebarAberta) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [sidebarAberta, fecharSidebar]);

  // Salvar preferências quando houver mudanças
  useEffect(() => {
    salvarPreferencias();
  }, [salvarPreferencias]);

  // Função para arredondar valores
  const arredondar = (valor: number): string => {
    return Math.round(valor) + "";
  };

  // Função para verificar se os inputs devem estar desabilitados
  const inputsDesabilitados = () => {
    return rodando || contandoRegressivo || tempo > 0;
  };

  // Função para atualizar café e recalcular água
  const handleCafeChange = (valor: string) => {
    if (inputsDesabilitados()) return;
    setCafe(valor);
    const numCafe = parseFloat(valor) || 0;
    const numProporcao = parseFloat(proporcao) || 0;
    if (numCafe > 0 && numProporcao > 0) {
      setAgua(arredondar(numCafe * numProporcao));
    }
  };

  // Função para atualizar água e recalcular café
  const handleAguaChange = (valor: string) => {
    if (aguaBloqueada || inputsDesabilitados()) return;
    setAgua(valor);
    const numAgua = parseFloat(valor) || 0;
    const numProporcao = parseFloat(proporcao) || 0;
    if (numAgua > 0 && numProporcao > 0) {
      setCafe(arredondar(numAgua / numProporcao));
    }
  };

  // Função para atualizar proporção e recalcular água
  const handleProporcaoChange = (valor: string) => {
    if (proporcaoBloqueada || inputsDesabilitados()) return;
    setProporcao(valor);
    const numCafe = parseFloat(cafe) || 0;
    const numProporcao = parseFloat(valor) || 0;
    if (numCafe > 0 && numProporcao > 0) {
      setAgua(arredondar(numCafe * numProporcao));
    }
  };

  // Função para selecionar todo o texto ao clicar
  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select();
  };

  // Função para desabilitar scroll e setas nos inputs
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "ArrowUp" || e.key === "ArrowDown") {
      e.preventDefault();
    }
  };

  const handleWheel = (e: React.WheelEvent<HTMLInputElement>) => {
    e.currentTarget.blur();
  };

  // Função para calcular a receita baseada no método 4:6
  const calcularReceita = useCallback(() => {
    const aguaTotal = parseFloat(agua) || 0;
    if (aguaTotal <= 0) return [];

    const receita = [];
    let aguaAcumulada = 0;

    // Primeiros 40% (2 despejos)
    const primeiros40 = aguaTotal * 0.4;
    let primeiro, segundo;

    if (perfilSabor === "Mais Acidez") {
      primeiro = Math.round(aguaTotal * 0.24);
      segundo = Math.round(aguaTotal * 0.16);
    } else if (perfilSabor === "Mais Doçura") {
      primeiro = Math.round(aguaTotal * 0.16);
      segundo = Math.round(aguaTotal * 0.24);
    } else { // Equilibrado
      primeiro = Math.round(primeiros40 / 2);
      segundo = Math.round(primeiros40 / 2);
    }

    // Adicionar primeiros dois despejos
    aguaAcumulada += primeiro;
    receita.push({
      tempo: "00:00",
      tempoSegundos: 0,
      quantidade: primeiro,
      total: aguaAcumulada
    });

    aguaAcumulada += segundo;
    receita.push({
      tempo: "00:45",
      tempoSegundos: 45,
      quantidade: segundo,
      total: aguaAcumulada
    });

    // Últimos 60%
    const ultimos60 = aguaTotal - aguaAcumulada;
    let despejos = [];

    if (perfilCorpo === "Menos Corpo") {
      // 2 despejos (30% cada)
      const despejo = Math.round(ultimos60 / 2);
      despejos = [despejo, ultimos60 - despejo];
    } else if (perfilCorpo === "Mais Corpo") {
      // 4 despejos (15% cada)
      const despejo = Math.round(ultimos60 / 4);
      despejos = [despejo, despejo, despejo, ultimos60 - (despejo * 3)];
    } else { // Equilibrado
      // 3 despejos (20% cada)
      const despejo = Math.round(ultimos60 / 3);
      despejos = [despejo, despejo, ultimos60 - (despejo * 2)];
    }

    // Adicionar despejos restantes
    const tempos = ["01:30", "02:15", "03:00", "03:45"];
    const temposSegundos = [90, 135, 180, 225];
    despejos.forEach((quantidade, index) => {
      aguaAcumulada += quantidade;
      receita.push({
        tempo: tempos[index],
        tempoSegundos: temposSegundos[index],
        quantidade,
        total: aguaAcumulada
      });
    });

    return receita;
  }, [agua, perfilSabor, perfilCorpo]);

  // Função para calcular o tempo total da receita
  const calcularTempoTotal = useCallback(() => {
    const receita = calcularReceita();
    if (receita.length === 0) return 0;

    const ultimoPasso = receita[receita.length - 1];
    return ultimoPasso.tempoSegundos + 45;
  }, [calcularReceita]);

  // Função para determinar o passo atual baseado no tempo do cronômetro
  const obterPassoAtual = useCallback(() => {
    const receita = calcularReceita();
    if (!rodando && tempo === 0) return -1; // Nenhum passo ativo se não iniciou

    // Encontrar o último passo que já deveria ter sido executado
    let passoAtual = -1;
    for (let i = 0; i < receita.length; i++) {
      if (tempo >= receita[i].tempoSegundos) {
        passoAtual = i;
      } else {
        break;
      }
    }

    return passoAtual;
  }, [calcularReceita, rodando, tempo]);

  // Funções do cronômetro
  useEffect(() => {
    if (rodando) {
      intervalRef.current = setInterval(() => {
        setTempo(prev => {
          const novoTempo = prev + 1;
          const tempoTotal = calcularTempoTotal();

          // Parar o cronômetro quando atingir o tempo total
          if (novoTempo >= tempoTotal) {
            setRodando(false);
            // Tracking da receita finalizada
            const receita = calcularReceita();
            trackRecipeCompleted(tempoTotal, receita.length);
            // Reproduzir áudio de finalização
            if (finishAudioRef.current) {
              finishAudioRef.current.currentTime = 0;
              finishAudioRef.current.play().catch(console.error);
            }
            return tempoTotal;
          }

          return novoTempo;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [rodando, calcularTempoTotal, calcularReceita]);

  // useEffect para contagem regressiva
  useEffect(() => {
    if (contandoRegressivo) {
      if (contagemRegressiva > 0) {
        regressivaRef.current = setTimeout(() => {
          setContagemRegressiva(prev => prev - 1);
        }, 1000);
      } else {
        setContandoRegressivo(false);
        setRodando(true);
        setContagemRegressiva(3); // Reset para próxima vez

        // Rolar para a receita em dispositivos mobile
        if (receitaRef.current && window.innerWidth < 768) {
          setTimeout(() => {
            receitaRef.current?.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }, 100);
        }
      }
    }

    return () => {
      if (regressivaRef.current) {
        clearTimeout(regressivaRef.current);
      }
    };
  }, [contandoRegressivo, contagemRegressiva]);

  // useEffect para inicializar os áudios
  useEffect(() => {
    audioRef.current = new Audio('/audio/countdown.mp3');
    audioRef.current.preload = 'auto';

    finishAudioRef.current = new Audio('/audio/finish.mp3');
    finishAudioRef.current.preload = 'auto';

    transitionAudioRef.current = new Audio('/audio/transition.mp3');
    transitionAudioRef.current.preload = 'auto';
    transitionAudioRef.current.volume = 0.5; // Reduzir volume

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
      if (finishAudioRef.current) {
        finishAudioRef.current.pause();
        finishAudioRef.current = null;
      }
      if (transitionAudioRef.current) {
        transitionAudioRef.current.pause();
        transitionAudioRef.current = null;
      }
    };
  }, []);

  const iniciarPausar = () => {
    if (rodando) {
      setRodando(false);
      trackRecipeTimer('pause', tempo);
      trackButtonClick('pausar_receita', 'calculadora');
    } else if (contandoRegressivo) {
      // Cancelar contagem regressiva
      setContandoRegressivo(false);
      setContagemRegressiva(3);
      trackRecipeTimer('reset');
      trackButtonClick('cancelar_contagem', 'calculadora');
      // Parar os áudios se estiverem tocando
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      if (finishAudioRef.current) {
        finishAudioRef.current.pause();
        finishAudioRef.current.currentTime = 0;
      }
      if (transitionAudioRef.current) {
        transitionAudioRef.current.pause();
        transitionAudioRef.current.currentTime = 0;
      }
    } else {
      // Se já foi iniciado antes (tempo > 0), iniciar diretamente sem contagem regressiva
      if (tempo > 0) {
        setRodando(true);
        trackRecipeTimer('resume', tempo);
        trackButtonClick('retomar_receita', 'calculadora');
        // Rolar para a receita em dispositivos mobile ao retomar
        if (receitaRef.current && window.innerWidth < 768) {
          setTimeout(() => {
            receitaRef.current?.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }, 100);
        }
      } else {
        // Primeira vez: iniciar contagem regressiva
        setContandoRegressivo(true);
        trackRecipeGenerated({
          cafe: parseFloat(cafe),
          agua: parseFloat(agua),
          proporcao: parseFloat(proporcao),
          perfilSabor,
          perfilCorpo
        });
        trackRecipeTimer('start');
        trackButtonClick('iniciar_receita', 'calculadora');
        // Reproduzir áudio do countdown
        if (audioRef.current) {
          audioRef.current.currentTime = 0;
          audioRef.current.play().catch(console.error);
        }
      }
    }
  };

  const resetar = () => {
    trackRecipeTimer('reset', tempo);
    trackButtonClick('reiniciar_receita', 'calculadora');
    setRodando(false);
    setContandoRegressivo(false);
    setTempo(0);
    setContagemRegressiva(3);
    passoAnteriorRef.current = -1; // Reset do passo anterior
    // Parar os áudios se estiverem tocando
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    if (finishAudioRef.current) {
      finishAudioRef.current.pause();
      finishAudioRef.current.currentTime = 0;
    }
    if (transitionAudioRef.current) {
      transitionAudioRef.current.pause();
      transitionAudioRef.current.currentTime = 0;
    }
  };

  const formatarTempo = (segundos: number): string => {
    const mins = Math.floor(segundos / 60);
    const secs = segundos % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };



  // useEffect para detectar mudanças de passo e reproduzir áudio de transição
  useEffect(() => {
    const passoAtual = obterPassoAtual();

    // Se mudou de passo e não é o primeiro passo (evita tocar no passo 0)
    if (passoAtual !== passoAnteriorRef.current && passoAtual > passoAnteriorRef.current && passoAtual > 0) {
      // Reproduzir áudio de transição
      if (transitionAudioRef.current) {
        transitionAudioRef.current.currentTime = 0;
        transitionAudioRef.current.play().catch(console.error);
      }
    }

    // Atualizar referência do passo anterior
    passoAnteriorRef.current = passoAtual;
  }, [obterPassoAtual]);
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header com menu hambúrguer pequeno */}
      <header className="fixed sm:absolute top-4 left-4 sm:top-6 sm:left-6 z-30">
        <button
          onClick={() => setSidebarAberta(true)}
          className="flex items-center gap-2 p-2 text-muted-foreground hover:text-primary hover:bg-accent/50 rounded-lg sm:rounded-lg rounded-full sm:bg-transparent bg-card/90 backdrop-blur-sm shadow-lg sm:shadow-none border sm:border-0 border-border/20 transition-all"
        >
          <Menu size={20} />
          <span className="text-sm font-medium hidden sm:inline">Menu</span>
        </button>
      </header>

      {/* Sidebar */}
      {sidebarAberta && (
        <>
          {/* Overlay */}
          <div
            className={`fixed inset-0 bg-black/50 z-40 ${sidebarFechando ? 'animate-fade-out' : 'animate-fade-in'}`}
            onClick={fecharSidebar}
          />

          {/* Sidebar content */}
          <div
            ref={sidebarRef}
            className={`fixed top-0 left-0 h-full w-80 bg-card border-r border-border shadow-lg z-50 ${sidebarFechando ? 'animate-slide-out-left' : 'animate-slide-in-left'}`}
          >
            {/* Sidebar header */}
            <div className="flex items-center justify-between p-6 border-b border-border">
              <div className="flex items-center gap-3">
                <Image
                  src="/cereja.png"
                  alt="Cereja"
                  width={40}
                  height={40}
                  className="object-contain"
                  quality={75}
                />
                <div>
                  <h2 className="text-xl font-bold text-foreground">cereja</h2>
                  <p className="text-sm text-muted-foreground">sua calculadora pessoal ♥</p>
                </div>
              </div>
              <button
                onClick={fecharSidebar}
                className="p-2 hover:bg-accent rounded-lg transition-colors"
              >
                <X size={20} />
              </button>
            </div>

            {/* Navigation menu */}
            <nav className="p-6">
              <div className="space-y-2">
                <Link
                  href="/"
                  onClick={fecharSidebar}
                  className="flex items-center gap-3 px-4 py-3 text-primary bg-primary/10 rounded-lg transition-colors"
                >
                  <HomeIcon size={20} />
                  <span className="font-medium">Calculadora 4:6</span>
                </Link>

                <Link
                  href="/glossario"
                  onClick={fecharSidebar}
                  className="flex items-center gap-3 px-4 py-3 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                >
                  <BookOpen size={20} />
                  <span className="font-medium">Glossário</span>
                </Link>
              </div>

              {/* Botão de doação */}
              <div className="mt-6 pt-6 border-t border-border">
                <a
                  href="https://ko-fi.com/pedrogott"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={fecharSidebar}
                  className="w-full flex items-center gap-3 px-4 py-3 text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl border border-orange-400/30 transform hover:scale-[1.02]"
                >
                  <Coffee size={20} />
                  <div className="flex flex-col text-left">
                    <span className="font-medium text-sm">Gostou do projeto?</span>
                    <span className="text-xs opacity-80">Pague-me um café!</span>
                  </div>
                </a>
              </div>

              {/* Link de Política de Privacidade - discreto */}
              <div className="mt-4 pt-4 border-t border-border/50">
                <Link
                  href="/privacidade"
                  onClick={fecharSidebar}
                  className="block px-4 py-2 text-xs text-muted-foreground hover:text-muted-foreground/80 transition-colors text-center"
                >
                  Política de Privacidade
                </Link>
              </div>
            </nav>
          </div>
        </>
      )}

      {/* Conteúdo principal */}
      <div className="flex flex-col items-center justify-start pt-2 sm:pt-4 px-4 sm:px-8 gap-4 sm:gap-8 flex-1">
        {/* Título grande centralizado */}
        <div className="flex items-center gap-2 sm:gap-3 mt-4 sm:mt-2">
          <Image
            src="/cereja.png"
            alt="Cereja"
            width={80}
            height={80}
            priority
            className="object-contain sm:w-[100px] sm:h-[100px]"
            quality={90}
          />
          <div className="text-center">
            <h1 className="text-4xl sm:text-6xl font-bold text-foreground">
              cereja
            </h1>
            <p className="text-xs sm:text-sm text-muted-foreground mt-1 sm:mt-2">
              calculadora do método 4:6
            </p>
          </div>
        </div>

      {/* Seção inferior: duas colunas responsivas */}
      <div className="w-full max-w-6xl grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8 items-start">
        <div className="bg-card p-4 sm:p-6 rounded-lg border border-border shadow-md">
          {/* Campos de entrada numéricos */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4 mb-4 sm:mb-6">
            <div>
              <label className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
                Café (g)
              </label>
              <input
                type="number"
                value={cafe}
                onChange={(e) => handleCafeChange(e.target.value)}
                onFocus={handleFocus}
                onKeyDown={handleKeyDown}
                onWheel={handleWheel}
                min="0"
                step="0.1"
                disabled={inputsDesabilitados()}
                className={`w-full px-3 py-2.5 sm:py-2 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
                  inputsDesabilitados()
                    ? "bg-muted text-muted-foreground cursor-not-allowed"
                    : "bg-background"
                }`}
              />
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
                Água (ml)
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={agua}
                  onChange={(e) => handleAguaChange(e.target.value)}
                  onFocus={handleFocus}
                  onKeyDown={handleKeyDown}
                  onWheel={handleWheel}
                  min="0"
                  step="0.1"
                  disabled={aguaBloqueada || inputsDesabilitados()}
                  className={`w-full px-3 py-2.5 sm:py-2 pr-12 sm:pr-10 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
                    aguaBloqueada || inputsDesabilitados()
                      ? "bg-muted text-muted-foreground cursor-not-allowed"
                      : "bg-background"
                  }`}
                />
                <button
                  type="button"
                  onClick={() => !inputsDesabilitados() && setAguaBloqueada(!aguaBloqueada)}
                  disabled={inputsDesabilitados()}
                  className={`absolute right-1 sm:right-2 top-1/2 transform -translate-y-1/2 p-2 sm:p-1 transition-colors min-w-[44px] min-h-[44px] sm:min-w-auto sm:min-h-auto flex items-center justify-center touch-target ${
                    inputsDesabilitados()
                      ? "text-muted-foreground cursor-not-allowed"
                      : "text-muted-foreground hover:text-foreground active:text-primary"
                  }`}
                >
                  {aguaBloqueada ? <Lock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} /> : <Unlock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} />}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
                Proporção (1:{proporcao})
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={proporcao}
                  onChange={(e) => handleProporcaoChange(e.target.value)}
                  onFocus={handleFocus}
                  onKeyDown={handleKeyDown}
                  onWheel={handleWheel}
                  min="0"
                  step="0.1"
                  disabled={proporcaoBloqueada || inputsDesabilitados()}
                  className={`w-full px-3 py-2.5 sm:py-2 pr-12 sm:pr-10 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
                    proporcaoBloqueada || inputsDesabilitados()
                      ? "bg-muted text-muted-foreground cursor-not-allowed"
                      : "bg-background"
                  }`}
                />
                <button
                  type="button"
                  onClick={() => !inputsDesabilitados() && setProporcaoBloqueada(!proporcaoBloqueada)}
                  disabled={inputsDesabilitados()}
                  className={`absolute right-1 sm:right-2 top-1/2 transform -translate-y-1/2 p-2 sm:p-1 transition-colors min-w-[44px] min-h-[44px] sm:min-w-auto sm:min-h-auto flex items-center justify-center touch-target ${
                    inputsDesabilitados()
                      ? "text-muted-foreground cursor-not-allowed"
                      : "text-muted-foreground hover:text-foreground active:text-primary"
                  }`}
                >
                  {proporcaoBloqueada ? <Lock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} /> : <Unlock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} />}
                </button>
              </div>
            </div>
          </div>

          {/* Perfil de Sabor */}
          <div className="mb-4 sm:mb-6">
            <h3 className="text-xs sm:text-sm font-medium text-foreground mb-2 sm:mb-3">
              Perfil de Sabor
            </h3>
            <div className="grid grid-cols-3 gap-2 sm:gap-3">
              {[
                { nome: "Mais Acidez", icone: "/images/acido.png" },
                { nome: "Equilibrado", icone: "/images/equilibrado.png" },
                { nome: "Mais Doçura", icone: "/images/docura.png" }
              ].map((opcao) => (
                <button
                  key={opcao.nome}
                  onClick={() => !inputsDesabilitados() && setPerfilSabor(opcao.nome)}
                  disabled={inputsDesabilitados()}
                  className={`px-3 sm:px-8 py-3 sm:py-4 rounded-md text-xs sm:text-sm font-medium transition-colors flex flex-col items-center gap-2 min-h-touch touch-target ${
                    inputsDesabilitados()
                      ? "cursor-not-allowed opacity-50"
                      : "cursor-pointer"
                  } ${
                    perfilSabor === opcao.nome
                      ? "bg-primary text-primary-foreground shadow-sm"
                      : "bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground shadow-xs hover:shadow-sm"
                  }`}
                >
                  <Image
                    src={opcao.icone}
                    alt={opcao.nome}
                    width={32}
                    height={32}
                    className="object-contain sm:w-[30px] sm:h-[30px]"
                    quality={75}
                  />
                  {opcao.nome}
                </button>
              ))}
            </div>
          </div>

          {/* Perfil de Corpo */}
          <div>
            <h3 className="text-xs sm:text-sm font-medium text-foreground mb-2 sm:mb-3">
              Perfil de Corpo
            </h3>
            <div className="grid grid-cols-3 gap-2 sm:gap-3">
              {[
                { nome: "Menos Corpo", icone: "/images/menos-corpo.png" },
                { nome: "Equilibrado", icone: "/images/equilibrado.png" },
                { nome: "Mais Corpo", icone: "/images/mais-corpo.png" }
              ].map((opcao) => (
                <button
                  key={opcao.nome}
                  onClick={() => !inputsDesabilitados() && setPerfilCorpo(opcao.nome)}
                  disabled={inputsDesabilitados()}
                  className={`px-3 sm:px-8 py-3 sm:py-4 rounded-md text-xs sm:text-sm font-medium transition-colors flex flex-col items-center gap-2 min-h-touch touch-target ${
                    inputsDesabilitados()
                      ? "cursor-not-allowed opacity-50"
                      : "cursor-pointer"
                  } ${
                    perfilCorpo === opcao.nome
                      ? "bg-primary text-primary-foreground shadow-sm"
                      : "bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground shadow-xs hover:shadow-sm"
                  }`}
                >
                  <Image
                    src={opcao.icone}
                    alt={opcao.nome}
                    width={32}
                    height={32}
                    className="object-contain sm:w-[30px] sm:h-[30px]"
                    quality={75}
                  />
                  {opcao.nome}
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="bg-card p-4 sm:p-6 rounded-lg border border-border shadow-md">
          <h2 className="text-xs sm:text-sm font-medium text-foreground mb-3 sm:mb-4">
            Cronômetro
          </h2>

          {/* Display do tempo */}
          <div className="flex flex-col items-center mb-1 sm:mb-2">
            <div className={`text-2xl sm:text-3xl font-bold px-3 sm:px-4 py-2 sm:py-3 rounded-lg border tabular-nums tracking-wider mb-2 transition-all duration-500 ${
              tempo >= calcularTempoTotal() && !contandoRegressivo
                ? "text-primary-foreground bg-primary border-primary shadow-lg animate-pulse"
                : tempo > 0 && !rodando && !contandoRegressivo
                ? "text-foreground bg-secondary border-border animate-pulse"
                : "text-foreground bg-secondary border-border"
            }`}>
              {contandoRegressivo ? contagemRegressiva : formatarTempo(tempo)}
            </div>

            {tempo >= calcularTempoTotal() && !contandoRegressivo && (
              <div className="text-center space-y-2 animate-in fade-in duration-700">
                <div className="text-lg sm:text-xl font-semibold text-primary">
                  ✨ Receita Finalizada! ✨
                </div>
                <div className="text-sm sm:text-base text-muted-foreground">
                  Aguarde a extração terminar e aproveite seu café! ☕
                </div>
              </div>
            )}
          </div>

          {/* Botões de controle */}
          <div className="flex justify-center gap-2 sm:gap-3">
            {tempo >= calcularTempoTotal() && !contandoRegressivo ? (
              // Receita finalizada: botões Compartilhar e Reiniciar
              <>
                <button
                  onClick={() => {
                    setModalAberto(true);
                    trackButtonClick('abrir_compartilhar', 'calculadora');
                  }}
                  className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-secondary text-secondary-foreground rounded-md text-xs font-normal hover:bg-accent hover:text-accent-foreground transition-colors min-h-touch touch-target"
                >
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="sm:w-4 sm:h-4">
                    <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                    <polyline points="16,6 12,2 8,6"/>
                    <line x1="12" y1="2" x2="12" y2="15"/>
                  </svg>
                  Compartilhar
                </button>
                <button
                  onClick={resetar}
                  className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-primary text-primary-foreground rounded-md text-xs font-normal hover:bg-primary/90 transition-colors min-h-touch touch-target"
                >
                  <RotateCcw size={18} className="sm:w-4 sm:h-4" />
                  Reiniciar
                </button>
              </>
            ) : (
              // Receita em andamento: botões normais
              <>
                <button
                  onClick={iniciarPausar}
                  className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-primary text-primary-foreground rounded-md text-xs font-normal hover:bg-primary/90 transition-colors min-h-touch touch-target"
                >
                  {rodando ? (
                    <>
                      <Pause size={18} className="sm:w-4 sm:h-4" />
                      Pausar
                    </>
                  ) : contandoRegressivo ? (
                    <>
                      <Pause size={18} className="sm:w-4 sm:h-4" />
                      Cancelar
                    </>
                  ) : tempo > 0 ? (
                    <>
                      <Play size={18} className="sm:w-4 sm:h-4" />
                      Retomar
                    </>
                  ) : (
                    <>
                      <Play size={18} className="sm:w-4 sm:h-4" />
                      Iniciar
                    </>
                  )}
                </button>

                {(tempo > 0 || rodando) && !contandoRegressivo && (
                  <button
                    onClick={resetar}
                    className="flex items-center gap-2 px-4 py-3 sm:py-2 bg-secondary text-secondary-foreground rounded-md text-xs font-normal hover:bg-accent hover:text-accent-foreground transition-colors min-h-touch touch-target"
                  >
                    <RotateCcw size={18} className="sm:w-4 sm:h-4" />
                    Reiniciar
                  </button>
                )}
              </>
            )}
          </div>

          {/* Barra de Progresso */}
          {(rodando || tempo > 0) && !contandoRegressivo && (
            <div className="w-full mt-4">
              <div className="relative">
                {/* Linha de fundo */}
                <div className="w-full h-1 bg-secondary rounded-full"></div>

                {/* Linha de progresso */}
                <div
                  className="absolute top-0 h-1 bg-primary rounded-full transition-all duration-1000 ease-out"
                  style={{
                    width: `${Math.min((tempo / calcularTempoTotal()) * 100, 100)}%`
                  }}
                />

                {/* Marcadores dos passos */}
                {calcularReceita().map((passo, index) => {
                  const posicao = (passo.tempoSegundos / calcularTempoTotal()) * 100;
                  const passoAtual = obterPassoAtual();
                  const isConcluido = index <= passoAtual;
                  const isAtivo = index === passoAtual;

                  return (
                    <div
                      key={index}
                      className="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2"
                      style={{ left: `${posicao}%` }}
                    >
                      <div
                        className={`w-0.5 h-2 transition-all duration-500 ${
                          isAtivo
                            ? 'bg-primary scale-110 shadow-md'
                            : isConcluido
                            ? 'bg-primary'
                            : 'bg-muted-foreground/60'
                        }`}
                      />
                    </div>
                  );
                })}

                {/* Marcador final */}
                <div
                  className="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2"
                  style={{ left: '100%' }}
                >
                  <div
                    className={`w-0.5 h-2 transition-all duration-500 ${
                      tempo >= calcularTempoTotal()
                        ? 'bg-primary'
                        : 'bg-muted-foreground/60'
                    }`}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Modal de Compartilhamento */}
          {modalAberto && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50 animate-in fade-in duration-300">
              <div className="bg-card border border-border rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto animate-in zoom-in-95 duration-300">
                {/* Header do Modal */}
                <div className="flex items-center justify-between p-4 border-b border-border">
                  <h3 className="text-lg font-semibold text-card-foreground">
                    Compartilhar Receita
                  </h3>
                  <button
                    onClick={() => setModalAberto(false)}
                    className="p-1 rounded-md hover:bg-accent transition-colors"
                  >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"/>
                      <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                  </button>
                </div>

                {/* Conteúdo do Modal */}
                <div className="p-4 space-y-4">
                  {/* Preview da Receita */}
                  <div className="bg-secondary rounded-lg p-4 text-sm">
                    <div className="font-semibold text-secondary-foreground mb-2">
                      ☕ Receita Cereja - Método 4:6
                    </div>
                    <div className="space-y-2 text-muted-foreground">
                      <div>
                        <strong>📊 Configuração:</strong>
                        <div className="ml-2">
                          • {cafe}g de café<br/>
                          • {agua}ml de água<br/>
                          • Proporção 1:{proporcao}<br/>
                          • Perfil: {perfilSabor} / {perfilCorpo}
                        </div>
                      </div>
                      <div>
                        <strong>⏱️ Passos:</strong>
                        <div className="ml-2">
                          {calcularReceita().map((passo, i) => (
                            <div key={i}>
                              {i + 1}. {passo.tempo} - {passo.quantidade}ml (Total: {passo.total}ml)
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        🌐 cereja-app.vercel.app
                      </div>
                    </div>
                  </div>

                  {/* Botões de Ação */}
                  <div className="flex gap-2">
                    <button
                      onClick={() => {
                        const receita = calcularReceita();
                        const texto = `☕ Receita Cereja - Método 4:6\n\n` +
                          `📊 Configuração:\n` +
                          `• ${cafe}g de café\n` +
                          `• ${agua}ml de água\n` +
                          `• Proporção 1:${proporcao}\n` +
                          `• Perfil: ${perfilSabor} / ${perfilCorpo}\n\n` +
                          `⏱️ Passos:\n` +
                          receita.map((passo, i) =>
                            `${i + 1}. ${passo.tempo} - ${passo.quantidade}ml (Total: ${passo.total}ml)`
                          ).join('\n') +
                          `\n\n🌐 cereja-app.vercel.app`;

                        if (navigator.clipboard && navigator.clipboard.writeText) {
                          navigator.clipboard.writeText(texto).then(() => {
                            trackRecipeShared('copy');
                            trackButtonClick('copiar_receita', 'modal_compartilhar');
                            setModalAberto(false);
                            // Mostrar feedback visual em vez de alert
                            const feedback = document.createElement('div');
                            feedback.textContent = 'Receita copiada! ✅';
                            feedback.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-4 py-2 rounded-lg shadow-lg z-50 animate-in slide-in-from-top duration-300';
                            document.body.appendChild(feedback);
                            setTimeout(() => {
                              feedback.remove();
                            }, 3000);
                          }).catch(() => {
                            alert('Não foi possível copiar a receita.');
                          });
                        } else {
                          // Fallback para navegadores mais antigos
                          const textArea = document.createElement('textarea');
                          textArea.value = texto;
                          textArea.style.position = 'fixed';
                          textArea.style.left = '-999999px';
                          textArea.style.top = '-999999px';
                          document.body.appendChild(textArea);
                          textArea.focus();
                          textArea.select();
                          try {
                            document.execCommand('copy');
                            trackRecipeShared('copy');
                            trackButtonClick('copiar_receita_fallback', 'modal_compartilhar');
                            setModalAberto(false);
                            const feedback = document.createElement('div');
                            feedback.textContent = 'Receita copiada! ✅';
                            feedback.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-4 py-2 rounded-lg shadow-lg z-50';
                            document.body.appendChild(feedback);
                            setTimeout(() => {
                              feedback.remove();
                            }, 3000);
                          } catch {
                            alert('Não foi possível copiar a receita.');
                          }
                          document.body.removeChild(textArea);
                        }
                      }}
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-primary text-primary-foreground rounded-md text-sm font-medium hover:bg-primary/90 transition-colors"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                        <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                      </svg>
                      Copiar Receita
                    </button>
                    <button
                      onClick={() => {
                        setModalAberto(false);
                        trackButtonClick('cancelar_compartilhar', 'modal_compartilhar');
                      }}
                      className="px-4 py-3 bg-secondary text-secondary-foreground rounded-md text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      Cancelar
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Receita passo a passo */}
          <div
            className={`mt-4 sm:mt-6 transition-all duration-700 ease-in-out ${
              tempo >= calcularTempoTotal() && !contandoRegressivo
                ? "opacity-0 max-h-0 overflow-hidden transform scale-95"
                : "opacity-100 max-h-[1000px] transform scale-100"
            }`}
            ref={receitaRef}
          >
            <div className="mb-3 sm:mb-4">
              <h3 className="text-xs sm:text-sm font-medium text-foreground">
                Receita
              </h3>
            </div>
            <div className="space-y-1.5 sm:space-y-2">
              {calcularReceita().map((passo, index) => {
                const passoAtual = obterPassoAtual();
                const receitaTerminada = tempo >= calcularTempoTotal() && !contandoRegressivo;
                const isAtivo = index === passoAtual && !receitaTerminada;
                const isConcluido = index < passoAtual || (index === passoAtual && receitaTerminada);
                const isProximo = index === passoAtual + 1 && !receitaTerminada;

                return (
                  <div
                    key={index}
                    className={`p-2.5 sm:p-3 rounded-md text-xs sm:text-sm transition-all duration-500 ease-in-out border-2 transform ${
                      isAtivo
                        ? "bg-primary/50 border-primary shadow-md scale-105"
                        : isConcluido
                        ? "bg-muted opacity-60 border-transparent scale-98"
                        : isProximo
                        ? "bg-accent/10 border-accent/25 scale-102"
                        : "bg-secondary/25 border-transparent scale-100"
                    }`}
                  >
                    <span className="text-foreground">
                      <span className={`inline-flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 rounded-full text-xs font-bold mr-2 transition-all duration-500 ease-in-out transform ${
                        isAtivo
                          ? "bg-primary text-primary-foreground animate-pulse scale-110"
                          : isConcluido
                          ? "bg-muted-foreground text-background scale-90"
                          : "bg-primary text-primary-foreground scale-100"
                      }`}>
                        {index + 1}
                      </span>
                      Aos <span className="font-bold">{passo.tempo}</span>, despeje <span className="font-bold">{passo.quantidade}ml</span> de água.{index > 0 && ` (Total ${passo.total}ml)`}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      </div>

      {/* Footer */}
      <footer className="bg-secondary/50 py-4 w-full text-center mt-8 sm:mt-0">
        <p className="text-xs text-muted-foreground">
          Desenvolvido por{" "}
          <a
            href="https://ko-fi.com/pedrogott"
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:text-primary/80 transition-colors font-medium"
          >
            Pedro Gottardi
          </a>
        </p>
      </footer>
    </div>
  );
}

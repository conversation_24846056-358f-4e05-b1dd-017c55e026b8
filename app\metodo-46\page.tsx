"use client";

import Image from "next/image";
import Link from "next/link";
import { useState, useMemo, useEffect, useRef, useCallback } from "react";
import { Search, Tag, X, Menu, Home as HomeIcon, BookOpen, ChevronUp, Coffee } from "lucide-react";
import {
  trackButtonClick,
  trackGlossaryTermViewed,
  trackGlossaryNavigation,
  trackGlossaryFilter
} from "@/lib/analytics";

// Tipos para os termos do glossário
interface TermoGlossario {
  id: string;
  termo: string;
  definicao: string;
  tags: string[];
}

// Dados dos termos do glossário
const termosGlossario: TermoGlossario[] = [
  {
    id: "espresso",
    termo: "Espresso",
    definicao: "Método de preparo de café que força água quente sob pressão através do café moído fino, resultando em uma bebida concentrada e encorpada com crema característica.",
    tags: ["Bebidas"]
  },
  {
    id: "arabica",
    termo: "Arábic<PERSON>",
    definicao: "Espécie originária da Etiópia (Coffea arabica), conhecida por representar cerca de 62% da produção mundial de café e ser a base dos cafés especiais, com notas sensoriais naturalmente adocicadas, ligeiramente ácidas, com aroma frutado e concentração menor de cafeína.",
    tags: ["Variedades"]
  },
  {
    id: "fermentacao",
    termo: "Fermentação",
    definicao: "Processo bioquímico que ocorre após a colheita do café, onde microrganismos quebram os açúcares presentes no fruto, influenciando significativamente o sabor final.",
    tags: ["Processamento"]
  },
  {
    id: "pour-over",
    termo: "Pour Over",
    definicao: "Método de preparo manual onde a água quente é despejada lentamente sobre o café moído em movimentos circulares, permitindo maior controle sobre a extração.",
    tags: ["Métodos"]
  },
  {
    id: "percolacao",
    termo: "Percolação",
    definicao: "Método de extração onde a água passa através do café moído por gravidade ou pressão, como no espresso, pour over e cafeteira italiana. A água dissolve os compostos solúveis do café durante sua passagem, permitindo controle sobre tempo e fluxo de extração.",
    tags: ["Métodos", "Preparo"]
  },
  {
    id: "imersao",
    termo: "Imersão",
    definicao: "Método de extração onde o café moído fica em contato direto com a água por um período determinado, como na prensa francesa e cold brew. Permite extração uniforme e controle preciso do tempo, resultando em sabores mais equilibrados e mais encorpados.",
    tags: ["Métodos", "Preparo"]
  },
  {
    id: "aeropress",
    termo: "AeroPress",
    definicao: "Método de preparo híbrido que combina imersão e pressão, criado por Alan Adler. Utiliza um cilindro com êmbolo para forçar a água através do café e filtro, produzindo uma bebida limpa, concentrada e versátil.",
    tags: ["Métodos"]
  },
  {
    id: "cafeteira-italiana",
    termo: "Cafeteira Italiana",
    definicao: "Também conhecida como Moka Pot, criada por Alfonso Bialetti em 1933. Utiliza pressão de vapor para forçar água quente através do café moído, produzindo uma bebida concentrada e encorpada, intermediária entre café filtrado e espresso.",
    tags: ["Métodos"]
  },
  {
    id: "chemex",
    termo: "Chemex",
    definicao: "Método de pour over criado por Peter Schlumbohm em 1941, caracterizado por seu design elegante em vidro e filtros especiais mais espessos. Produz café limpo e claro, realçando acidez e notas delicadas, com corpo mais leve.",
    tags: ["Métodos"]
  },
  {
    id: "hario-v60",
    termo: "Hario v60",
    definicao: "Coador cônico japonês com espirais internas e um grande orifício central, permitindo fluxo rápido e uniforme. Oferece grande controle sobre a extração através da técnica de despejo, produzindo café brilhante com acidez pronunciada e clareza de sabores.",
    tags: ["Métodos"]
  },
  {
    id: "kalita-wave",
    termo: "Kalita Wave",
    definicao: "Coador japonês com fundo plano e três pequenos orifícios, projetado para extração mais uniforme e consistente. Os filtros ondulados reduzem o contato com as paredes, promovendo fluxo controlado e café equilibrado.",
    tags: ["Métodos"]
  },
  {
    id: "prensa-francesa",
    termo: "Prensa Francesa",
    definicao: "Método de imersão total onde café moído grosso fica em contato com água quente, sendo separado por um filtro de metal. Produz café encorpado, com óleos naturais preservados e sabor intenso, também conhecida como French Press.",
    tags: ["Métodos"]
  },
  {
    id: "clever",
    termo: "Clever",
    definicao: "Método híbrido que combina imersão e percolação, utilizando uma válvula que controla o fluxo. O café fica em imersão até ser colocado sobre a xícara, quando a válvula abre e drena. Oferece consistência da imersão com limpeza da filtragem.",
    tags: ["Métodos"]
  },
  {
    id: "sifao",
    termo: "Sifão",
    definicao: "Método que utiliza vácuo e pressão de vapor em duas câmaras de vidro. A água sobe para a câmara superior onde extrai o café por imersão, retornando por vácuo. Produz café limpo e aromático..",
    tags: ["Métodos"]
  },
  {
    id: "melitta",
    termo: "Melitta",
    definicao: "Método pioneiro de café coado criado por Melitta Bentz em 1908 na Alemanha, utilizando filtro de papel cônico. Base de todos os métodos de pour over modernos, caracterizado pela simplicidade e eficiência na produção de café limpo e equilibrado.",
    tags: ["Métodos"]
  },
  {
    id: "torra-media",
    termo: "Torra Média",
    definicao: "Nível de torra entre o primeiro e segundo crack, equilibrando acidez e amargor. Preserva características originais do terroir enquanto desenvolve sabores caramelizados e corpo moderado. Ideal para métodos filtrados e espresso, oferecendo complexidade sem mascarar origem.",
    tags: ["Torra"]
  },
  {
    id: "fermentado",
    termo: "Fermentado",
    definicao: "Característica sensorial resultante de processos fermentativos controlados ou naturais, manifestando-se como notas vinosas, frutadas intensas, acéticas ou alcoólicas. Pode ser desejável em cafés especiais experimentais ou indicar defeito quando excessivo, criando sabores únicos e complexos.",
    tags: ["Degustação"]
  },
  {
    id: "affogato",
    termo: "Affogato",
    definicao: "Sobremesa italiana que combina uma dose de espresso quente despejada sobre uma bola de sorvete de baunilha, criando um contraste entre quente e frio.",
    tags: ["Bebidas"]
  },
  {
    id: "americano",
    termo: "Americano",
    definicao: "Conhecido também como 'Carioca', é uma bebida preparada adicionando água quente ao espresso, resultando em um café com força similar ao café filtrado, mas mantendo as características do espresso.",
    tags: ["Bebidas"]
  },
  {
    id: "cappuccino",
    termo: "Cappuccino",
    definicao: "Bebida italiana tradicional composta por espresso, leite vaporizado e espuma de leite em proporções iguais.",
    tags: ["Bebidas"]
  },
  {
    id: "cold-brew",
    termo: "Cold Brew",
    definicao: "Método de extração a frio onde o café moído grosso fica em infusão com água fria por 12-24 horas, resultando em uma bebida suave e menos ácida.",
    tags: ["Bebidas"]
  },
  {
    id: "cortado",
    termo: "Cortado",
    definicao: "Duplo ristretto servido em um copo pequeno, com uma pequena quantidade de café com nenhuma ou pouca espuma. Equilibrando a acidez do duplo ristretto com o dulçor do leite.",
    tags: ["Bebidas"]
  },
  {
    id: "flat-white",
    termo: "Flat White",
    definicao: "Bebida originária da Austrália/Nova Zelândia, feita com espresso duplo e leite vaporizado com microespuma, criando uma textura aveludada e sabor intenso.",
    tags: ["Bebidas"]
  },
  {
    id: "latte",
    termo: "Latte",
    definicao: "Bebida suave composta por espresso e uma grande quantidade de leite vaporizado, finalizada com uma fina camada de espuma, ideal para latte art.",
    tags: ["Bebidas"]
  },
  {
    id: "lungo",
    termo: "Lungo",
    definicao: "Espresso 'longo' extraído com mais água e tempo de extração, resultando em uma bebida mais volumosa e com sabor mais amargo que o espresso tradicional.",
    tags: ["Bebidas", "Espresso"]
  },
  {
    id: "macchiato",
    termo: "Macchiato",
    definicao: "Espresso 'manchado' com uma pequena quantidade de espuma de leite, preservando o sabor intenso do café com apenas um toque de suavidade.",
    tags: ["Bebidas"]
  },
  {
    id: "ristretto",
    termo: "Ristretto",
    definicao: "Espresso 'restrito' extraído com menos água, resultando em uma bebida mais concentrada, doce e aromática, considerada a essência pura do café.",
    tags: ["Bebidas", "Espresso"]
  },
  {
    id: "acaia",
    termo: "Acaiá",
    definicao: "Seleção da variedade Mundo Novo, conhecida por seus grãos grandes (nome deriva do tupi-guarani que significa \"frutos com sementes grandes\"), com notas sensoriais suaves que costumam apresentar nuances florais, frutadas e achocolatadas.",
    tags: ["Variedades"]
  },
  {
    id: "arara",
    termo: "Arara",
    definicao: "Híbrida natural entre Obatã e Catuaí Amarelo, conhecida por sua alta resistência à ferrugem do cafeeiro e excelente produtividade, com notas sensoriais de boa qualidade, frutos amarelos e alta tolerância à seca.",
    tags: ["Variedades"]
  },
  {
    id: "blend",
    termo: "Blend",
    definicao: "Mistura de diferentes variedades ou origens de café para criar um perfil de sabor específico e consistente.",
    tags: ["Variedades"]
  },
  {
    id: "bourbon-amarelo",
    termo: "Bourbon Amarelo",
    definicao: "Mutação natural do Bourbon Vermelho descoberta em São Paulo em 1930, conhecida por ser mais doce e 40% mais produtiva que o vermelho, com notas sensoriais de mel, acidez cítrica acentuada, caramelo e frutas cítricas.",
    tags: ["Variedades"]
  },
  {
    id: "bourbon-vermelho",
    termo: "Bourbon Vermelho",
    definicao: "Variedade de Bourbon, conhecida por sua qualidade excepcional em cafés especiais, com notas sensoriais de chocolate, avelã, baunilha e frutas vermelhas com corpo encorpado",
    tags: ["Variedades"]
  },
  {
    id: "catuai",
    termo: "Catuaí",
    definicao: "Híbrida entre Mundo Novo e Caturra, conhecida por sua alta produtividade e porte baixo facilitando a colheita, com notas sensoriais equilibradas, doces e com boa qualidade de bebida.",
    tags: ["Variedades"]
  },
  {
    id: "catuai-amarelo",
    termo: "Catuaí Amarelo",
    definicao: "Variedade de Catuaí com frutos amarelos, conhecida por sua vivacidade e sabor equilibrado, com notas sensoriais cítricas, florais e sutis notas de noz com acidez brilhante.",
    tags: ["Variedades"]
  },
  {
    id: "catuai-vermelho",
    termo: "Catuaí Vermelho",
    definicao: "Variedade de Catuaí com frutos vermelhos, conhecida por emergir do cruzamento histórico de 1949, com notas sensoriais de frutas de caroço, chocolate e manteiga.",
    tags: ["Variedades"]
  },
  {
    id: "catucai",
    termo: "Catucaí",
    definicao: "Híbrida natural entre Icatu e Catuaí desenvolvida no Rio de Janeiro em 1988, conhecida por sua resistência moderada à ferrugem e alta produtividade, com notas sensoriais de boa qualidade semelhante ao Catuaí.",
    tags: ["Variedades"]
  },
  {
    id: "catucai-amarelo",
    termo: "Catucaí Amarelo",
    definicao: "Variedade de Catucaí com frutos amarelos, conhecida por sua acidez cítrica elevada e doçura, com notas sensoriais que nas regiões da Mantiqueira apresentam características cítricas com doçura elevada.",
    tags: ["Variedades"]
  },
  {
    id: "catucai-vermelho",
    termo: "Catucaí Vermelho",
    definicao: "Variedade de Catucaí com frutos vermelhos, conhecida por ser de porte baixo com maturação muito precoce, com notas sensoriais complexas que variam conforme a região de cultivo.",
    tags: ["Variedades"]
  },
  {
    id: "caturra",
    termo: "Caturra",
    definicao: "Mutação natural da variedade Bourbon descoberta no Brasil na região entre Minas Gerais e Espírito Santo, conhecida por seu porte baixo e facilidade de colheita, com notas sensoriais equilibradas, aromáticas com lembrança de avelã e chocolate.",
    tags: ["Variedades"]
  },
  {
    id: "geisha",
    termo: "Geisha (Gesha)",
    definicao: "Variedade originária da vila Gesha na Etiópia. Conhecida por ser uma das mais renomadas mundialmente após ganhar competições, com notas sensoriais florais, cítricas e perfil redondo e persistente.",
    tags: ["Variedades"]
  },
  {
    id: "icatu",
    termo: "Icatu",
    definicao: "Híbrida interespecífica entre Typica e Mundo Novo, conhecida por sua resistência à ferrugem e alta produtividade, com notas florais, chocolate e notas cítricas.",
    tags: ["Variedades"]
  },
  {
    id: "java",
    termo: "Java",
    definicao: "Variedade tradicional cultivada na Indonésia, descendente do Typica. Conhecida por seu corpo pesado, baixa acidez e notas terrosas características.",
    tags: ["Variedades"]
  },
  {
    id: "maragogipe",
    termo: "Maragogipe",
    definicao: "Mutação natural da Typica originária da Bahia, conhecida pelos grãos gigantes chamados \"grão-elefante\", com notas sensoriais de goiaba, tangerina, uvaia, acerola e melaço, sendo mais suave que grãos arábica padrão.",
    tags: ["Variedades"]
  },
  {
    id: "mokka",
    termo: "Mokka (Mocha)",
    definicao: "Variedade derivada do porto de Al-Mokha no Iêmen, conhecida historicamente por ter sabor naturalmente achocolatado, com notas sensoriais que remetem ao chocolate.",
    tags: ["Variedades"]
  },
  {
    id: "mundo-novo",
    termo: "Mundo Novo",
    definicao: "Híbrida natural entre Typica e Bourbon, conhecida por sua alta produtividade e resistência, com notas sensoriais de corpo licoroso, alta doçura e acidez moderada.",
    tags: ["Variedades"]
  },
  {
    id: "pacamara",
    termo: "Pacamara",
    definicao: "Híbrida entre Pacas e Maragogipe, conhecida pelos grãos grandes e qualidade excepcional, com notas de frutas tropicais, caramelo e chocolate, além de uma acidez cítrica marcante e um corpo leve e sedoso.",
    tags: ["Variedades"]
  },
  {
    id: "pacas",
    termo: "Pacas",
    definicao: "Mutação natural do Bourbon, conhecida por sua importância histórica na cafeicultura, com notas sensoriais que incluem doçura, corpo aveludado e acidez equilibrada, com frequentes descrições de caramelo e chocolate.",
    tags: ["Variedades"]
  },
  {
    id: "robusta",
    termo: "Robusta",
    definicao: "Espécie Coffea canephora, conhecida por sua resistência a pragas e maior teor de cafeína, com notas sensoriais mais encorpadas, amargas e terrosas.",
    tags: ["Variedades"]
  },
  {
    id: "single-origin",
    termo: "Single Origin",
    definicao: "Café proveniente de uma única origem geográfica, seja fazenda, região ou país, preservando características únicas do terroir.",
    tags: ["Variedades"]
  },
  {
    id: "sl28",
    termo: "SL28",
    definicao: "Variedade desenvolvida no Quênia, conhecida por sua resistência à seca e qualidade excepcional, com notas sensoriais de suco de laranja com amora, doçura de caramelo, mel, acidez cítrica e corpo licoroso.",
    tags: ["Variedades"]
  },
  {
    id: "topazio",
    termo: "Topázio",
    definicao: "Híbrida entre Catuaí Amarelo e Mundo Novo, com notas sensoriais cítricas, frutadas, florais e achocolatadas",
    tags: ["Variedades"]
  },
  {
    id: "typica",
    termo: "Typica",
    definicao: "Uma das variedades mais antigas e importantes do café arábica com presença no Brasil, conhecida por sua elegância e alto potencial de qualidade, notas sensoriais de acidez suave e equilibrada, doçura e aromas florais.",
    tags: ["Variedades"]
  },
  {
    id: "altitude",
    termo: "Altitude",
    definicao: "Altura em relação ao nível do mar onde o café é cultivado. Altitudes elevadas (acima de 1000m) proporcionam temperaturas mais baixas, maior amplitude térmica e desenvolvimento mais lento dos frutos, resultando em grãos mais densos e com maior complexidade de sabores.",
    tags: ["Cultivo"]
  },
  {
    id: "brix",
    termo: "Brix",
    definicao: "Medida que indica a concentração de açúcares solúveis no fruto do café. O grau Brix é fundamental para determinar o ponto ideal de colheita, pois frutos com maior concentração de açúcar tendem a produzir cafés de melhor qualidade.",
    tags: ["Cultivo"]
  },
  {
    id: "cereja",
    termo: "Cereja",
    definicao: "Estágio de maturação ideal do fruto do café, quando apresenta coloração vermelha intensa e uniforme. Neste ponto, o fruto possui máximo potencial de qualidade, com concentração ideal de açúcares e desenvolvimento completo dos compostos aromáticos.",
    tags: ["Cultivo"]
  },
  {
    id: "densidade",
    termo: "Densidade",
    definicao: "Característica física do grão de café que indica sua compactação e qualidade. Grãos mais densos, geralmente cultivados em altitudes elevadas, possuem maior concentração de compostos aromáticos e tendem a produzir cafés de melhor qualidade.",
    tags: ["Cultivo"]
  },
  {
    id: "mucilagem",
    termo: "Mucilagem",
    definicao: "Camada gelatinosa e pegajosa (mesocarpo) que envolve o grão de café, localizada entre a polpa e o pergaminho. Rica em açúcares, é fundamental nos processos de fermentação e influencia diretamente no desenvolvimento dos sabores do café.",
    tags: ["Cultivo"]
  },
  {
    id: "pergaminho",
    termo: "Pergaminho",
    definicao: "Camada fina e resistente (endocarpo) que envolve diretamente o grão de café, localizada entre a mucilagem e a película prateada. É removida durante o processo de beneficiamento, sendo essencial para proteger o grão durante o processamento.",
    tags: ["Cultivo"]
  },
  {
    id: "polpa",
    termo: "Polpa",
    definicao: "Camada externa carnosa do fruto do café (exocarpo), que protege as sementes. Durante o processamento, pode ser removida imediatamente após a colheita (método lavado) ou mantida durante a secagem (método natural), influenciando o perfil sensorial final.",
    tags: ["Cultivo"]
  },
  {
    id: "terroir",
    termo: "Terroir",
    definicao: "Conjunto de características ambientais únicas de uma região produtora, incluindo clima, solo, altitude, topografia e práticas de cultivo. O terroir confere características distintivas ao café, criando perfis sensoriais únicos e identificáveis de cada origem.",
    tags: ["Cultivo"]
  },
  {
    id: "acidez",
    termo: "Acidez",
    definicao: "Característica sensorial que confere vivacidade e brilho ao café, percebida principalmente nas laterais da língua. Quando equilibrada, a acidez define um café de qualidade, podendo lembrar frutas cítricas, maçã verde ou vinho. Não deve ser confundida com azedume, que é um defeito.",
    tags: ["Degustação"]
  },
  {
    id: "amargor",
    termo: "Amargor",
    definicao: "Propriedade sensorial causada por substâncias como cafeína, trigonelina e compostos fenólicos, percebida principalmente no fundo da língua. O amargor equilibrado é desejável no café, contribuindo para a complexidade da bebida, mas em excesso pode ser considerado um defeito.",
    tags: ["Degustação"]
  },
  {
    id: "aroma",
    termo: "Aroma",
    definicao: "Percepção olfativa dos compostos voláteis liberados pelo café, captada pelo nariz antes e durante a degustação. O aroma pode revelar centenas de compostos diferentes, incluindo notas florais, frutadas, achocolatadas, especiarias ou amadeiradas, sendo fundamental na experiência sensorial.",
    tags: ["Degustação"]
  },
  {
    id: "corpo",
    termo: "Corpo",
    definicao: "Sensação tátil de peso, textura e viscosidade do café na boca, relacionada à presença de óleos, açúcares e sedimentos. Pode variar de leve e delicado até encorpado e cremoso, sendo percebido principalmente no meio da língua e contribuindo para a sensação geral da bebida.",
    tags: ["Degustação"]
  },
  {
    id: "cupping",
    termo: "Cupping",
    definicao: "Método padronizado de degustação de café desenvolvido pela Specialty Coffee Association (SCA), usado para avaliar e pontuar cafés de forma objetiva. O protocolo envolve análise de aroma, sabor, acidez, corpo, finalização e outros atributos sensoriais em condições controladas.",
    tags: ["Degustação", "Profissional"]
  },
  {
    id: "docura",
    termo: "Doçura",
    definicao: "Característica sensorial natural do café, resultante dos açúcares presentes no grão que se caramelizam durante a torra. A doçura equilibrada é um indicativo de qualidade, podendo lembrar caramelo, mel, frutas maduras ou chocolate, sem necessidade de adoçantes externos.",
    tags: ["Degustação"]
  },
  {
    id: "finalizacao",
    termo: "Finalização",
    definicao: "Também conhecida como retrogosto ou aftertaste, é a sensação que permanece na boca após engolir o café. Uma boa finalização deve ser longa, limpa e prazerosa, podendo revelar novas notas sensoriais e contribuindo significativamente para a qualidade geral da experiência.",
    tags: ["Degustação"]
  },
  {
    id: "notas-sensoriais",
    termo: "Notas Sensoriais",
    definicao: "Descritores específicos usados para identificar e comunicar os sabores e aromas presentes no café. Incluem mais de 110 atributos como frutas, flores, especiarias, nozes e outros, ajudando a caracterizar o perfil único de cada café.",
    tags: ["Degustação"]
  },
  {
    id: "tds",
    termo: "TDS",
    definicao: "Total Dissolved Solids (Sólidos Totais Dissolvidos) - medida que indica a concentração de compostos extraídos do café na bebida final, expressa em porcentagem. O TDS ideal varia entre 1,15% e 1,35% para métodos filtrados, sendo fundamental para avaliar a força e qualidade da extração.",
    tags: ["Degustação"]
  },
  {
    id: "balanca-precisao",
    termo: "Balança de Precisão",
    definicao: "Equipamento essencial para medir com exatidão a quantidade de café e água no preparo. Com precisão de 0,1g, permite controlar a proporção café-água, garantindo consistência e qualidade na extração. Fundamental para métodos como pour over, espresso e cupping.",
    tags: ["Equipamento"]
  },
  {
    id: "burr",
    termo: "Burr (moedor)",
    definicao: "Sistema de moagem que utiliza duas rebarbas (cônicas ou planas) para triturar os grãos de café de forma uniforme. Superior aos moedores de lâminas, produz granulometria consistente e permite ajuste preciso da moagem, essencial para extrações de qualidade.",
    tags: ["Equipamento"]
  },
  {
    id: "chaleira-bico-ganso",
    termo: "Chaleira Bico de Ganso",
    definicao: "Chaleira com bico fino, longo e curvo, projetada para métodos de café coado como pour over. Permite controle preciso do fluxo de água, velocidade e direção do despejo, fundamentais para uma extração uniforme e controlada.",
    tags: ["Equipamento"]
  },
  {
    id: "cronometro",
    termo: "Cronômetro",
    definicao: "Instrumento para medir o tempo de extração do café, crucial para controlar a qualidade da bebida. Usado em espresso (25-30 segundos), pour over (3-4 minutos) e outros métodos, garantindo consistência e permitindo ajustes na receita.",
    tags: ["Equipamento"]
  },
  {
    id: "filtro",
    termo: "Filtro",
    definicao: "Elemento que separa o pó de café da bebida final durante a extração. Pode ser de papel (mais limpo), metal (mais corpo) ou tecido. Cada tipo influencia o sabor final, retendo óleos e sedimentos em diferentes graus.",
    tags: ["Equipamento"]
  },
  {
    id: "porta-filtro",
    termo: "Porta-filtro",
    definicao: "Componente da máquina de espresso que contém o café moído durante a extração. Possui uma cesta onde o café é compactado e se acopla ao grupo da máquina para a passagem da água pressurizada.",
    tags: ["Equipamento", "Espresso"]
  },
  {
    id: "refratometro",
    termo: "Refratômetro",
    definicao: "Instrumento de precisão que mede o TDS (Total Dissolved Solids) do café, indicando a concentração da bebida. Permite avaliar objetivamente a qualidade da extração, ajustar receitas e manter consistência no preparo.",
    tags: ["Equipamento", "Profissional"]
  },
  {
    id: "tamper",
    termo: "Tamper",
    definicao: "Ferramenta usada para compactar uniformemente o café moído no porta-filtro antes da extração do espresso. A compactação adequada garante resistência uniforme à passagem da água, evitando canalização e promovendo extração equilibrada.",
    tags: ["Equipamento"]
  },
  {
    id: "pitcher",
    termo: "Pitcher",
    definicao: "Jarra de aço inoxidável com bico específico para vaporizar leite e criar latte art. Seu design permite controle preciso do fluxo de leite vaporizado, sendo essencial para bebidas como cappuccino, latte e flat white.",
    tags: ["Equipamento"]
  },
  {
    id: "compactacao",
    termo: "Compactação",
    definicao: "Processo de pressionar uniformemente o café moído no porta-filtro usando um tamper, criando resistência adequada à passagem da água. A compactação correta evita canalização, garante extração uniforme e é fundamental para um espresso de qualidade.",
    tags: ["Espresso"]
  },
  {
    id: "crema",
    termo: "Crema",
    definicao: "Camada espessa e cremosa de cor dourada que se forma na superfície do espresso, resultado da emulsificação de óleos do café sob pressão. Indica frescor dos grãos e qualidade da extração, contribuindo para aroma, textura e experiência sensorial.",
    tags: ["Espresso"]
  },
  {
    id: "channeling",
    termo: "Channeling",
    definicao: "Também conhecido como canalização, é um defeito na extração do espresso onde a água encontra caminhos de menor resistência através do café moído, causando fluxo desigual. Resulta em sub-extração em algumas áreas e sobre-extração em outras, prejudicando o sabor final.",
    tags: ["Espresso"]
  },
  {
    id: "ataques",
    termo: "Ataques",
    definicao: "Técnica de despejo em métodos pour over onde a água é adicionada em etapas controladas, permitindo melhor controle da extração. Cada ataque tem volume e tempo específicos, influenciando o fluxo, temperatura e desenvolvimento dos sabores durante o preparo.",
    tags: ["Preparo"]
  },
  {
    id: "bloom",
    termo: "Bloom",
    definicao: "Fase inicial do preparo onde uma pequena quantidade de água é despejada sobre o café moído, causando liberação de CO2 e expansão do pó. Também chamado de pré-infusão, dura 30-45 segundos e prepara o café para extração uniforme.",
    tags: ["Preparo"]
  },
  {
    id: "granulometria",
    termo: "Granulometria",
    definicao: "Tamanho das partículas do café após a moagem, fundamental para controlar a extração. Moagem fina aumenta a superfície de contato e acelera a extração (risco de sobre-extração), enquanto moagem grossa reduz o contato e desacelera o processo (risco de sub-extração), devendo ser ajustada conforme o método.",
    tags: ["Preparo"]
  },
  {
    id: "moagem",
    termo: "Moagem",
    definicao: "Processo de triturar os grãos de café em partículas menores para permitir a extração dos compostos solúveis. A qualidade e uniformidade da moagem afetam diretamente o sabor final, sendo essencial usar moedores adequados e ajustar conforme o método de preparo.",
    tags: ["Preparo"]
  },
  {
    id: "proporcao",
    termo: "Proporção",
    definicao: "Relação entre a quantidade de café e água no preparo, também conhecida como brew ratio. Expressa como 1:15 (1g de café para 15g de água), por exemplo. A proporção adequada é fundamental para equilibrar força, sabor e qualidade da extração.",
    tags: ["Preparo"]
  },
  {
    id: "sobre-extracao",
    termo: "Sobre-extração",
    definicao: "Condição onde compostos indesejáveis são extraídos em excesso, resultando em sabor amargo, adstringente e desbalanceado. Causada por moagem muito fina, tempo excessivo, temperatura alta ou proporção inadequada. Pode ser corrigida ajustando os parâmetros de preparo.",
    tags: ["Preparo"]
  },
  {
    id: "sub-extracao",
    termo: "Sub-extração",
    definicao: "Condição onde poucos compostos são extraídos do café, resultando em sabor ácido, azedo e aguado. Causada por moagem muito grossa, tempo insuficiente, temperatura baixa ou proporção inadequada. Indica que o potencial do café não foi totalmente aproveitado.",
    tags: ["Preparo"]
  },
  {
    id: "tempo-imersao",
    termo: "Tempo de Imersão",
    definicao: "Duração que o café moído permanece em contato direto com a água em métodos de imersão. Varia conforme o método: 4 minutos na prensa francesa, 12-24 horas no cold brew. Controla a intensidade da extração e o perfil de sabor final.",
    tags: ["Preparo"]
  },
  {
    id: "natural-seco",
    termo: "Natural (Seco)",
    definicao: "Método de processamento mais antigo onde os frutos são secos inteiros ao sol com polpa e mucilagem. Produz cafés mais encorpados, doçura intensa e notas frutadas complexas, sendo tradicionalmente usado no Brasil e Etiópia.",
    tags: ["Processamento"]
  },
  {
    id: "lavado-fully-washed",
    termo: "Lavado (Fully Washed)",
    definicao: "Método onde polpa e mucilagem são completamente removidas antes da secagem. Resulta em cafés limpos, com acidez pronunciada e clareza de sabores, permitindo expressar características do terroir.",
    tags: ["Processamento"]
  },
  {
    id: "honey-process",
    termo: "Honey Process",
    definicao: "Método híbrido onde apenas a polpa é removida, mantendo parte da mucilagem durante a secagem. Varia conforme quantidade de mucilagem: Yellow Honey (menos mucilagem, mais limpo), Red Honey (mucilagem moderada, equilibrado) e Black Honey (máxima mucilagem, mais doce e complexo).",
    tags: ["Processamento"]
  },
  {
    id: "beneficiamento",
    termo: "Beneficiamento",
    definicao: "Conjunto de processos pós-colheita que transformam o café cereja em café verde, incluindo secagem, descascamento, classificação e armazenamento. Etapa crucial que preserva qualidade e prepara o café para comercialização e torra.",
    tags: ["Processamento"]
  },
  {
    id: "cafe-verde",
    termo: "Café Verde",
    definicao: "Grão de café cru após beneficiamento, antes da torra. Mantém características originais do terroir e processamento, sendo a forma comercializada entre produtores e torrefadores. Pode ser armazenado por meses mantendo qualidade quando bem conservado.",
    tags: ["Processamento"]
  },
  {
    id: "choque-termico",
    termo: "Choque Térmico",
    definicao: "Técnica experimental de processamento onde o café é submetido a mudanças bruscas de temperatura durante fermentação ou secagem. Pode intensificar características sensoriais e criar perfis únicos, sendo usado em cafés especiais inovadores.",
    tags: ["Processamento"]
  },
  {
    id: "descafeinado",
    termo: "Descafeinado",
    definicao: "Café com cafeína removida através de processos químicos ou naturais antes da torra. Métodos incluem água, solventes ou CO2 supercrítico. Mantém sabor similar ao original, permitindo consumo sem efeitos estimulantes da cafeína.",
    tags: ["Processamento"]
  },
  {
    id: "kopi-luwak",
    termo: "Kopi Luwak",
    definicao: "Café exótico indonésio produzido a partir de grãos consumidos e excretados pela civeta (luwak). O processo digestivo altera características dos grãos, criando sabor único e suave. Considerado um dos cafés mais caros do mundo devido à raridade e processo peculiar.",
    tags: ["Processamento"]
  },
  {
    id: "jacu",
    termo: "Jacu",
    definicao: "Café brasileiro produzido a partir de grãos consumidos pelo pássaro jacu na Mata Atlântica. Similar ao Kopi Luwak, o processo digestivo da ave seleciona e modifica os grãos, resultando em café raro com características sensoriais únicas e valor elevado.",
    tags: ["Processamento"]
  },
  {
    id: "fermentacao-anaerobica",
    termo: "Fermentação Anaeróbica",
    definicao: "Processo fermentativo em ambiente sem oxigênio, usando tanques selados. Cria condições controladas que desenvolvem sabores únicos e intensos, frequentemente frutados e vinosos. Técnica experimental que permite maior controle sobre perfil sensorial final.",
    tags: ["Processamento"]
  },
  {
    id: "fermentacao-latica",
    termo: "Fermentação Lática",
    definicao: "Processo onde bactérias láticas convertem açúcares em ácido lático, criando características sensoriais específicas. Pode ocorrer naturalmente ou ser induzida, resultando em cafés com acidez diferenciada e notas complexas, frequentemente cremosas e frutadas.",
    tags: ["Processamento"]
  },
  {
    id: "fermentacao-koji",
    termo: "Fermentação Koji",
    definicao: "Técnica experimental usando fungo Aspergillus oryzae (koji), tradicionalmente usado em fermentados asiáticos. Aplicado ao café, produz enzimas que modificam compostos do grão, criando perfis sensoriais únicos com notas umami e complexidade diferenciada.",
    tags: ["Processamento"]
  },
  {
    id: "maceracao-carbonica",
    termo: "Maceração Carbônica",
    definicao: "Processo adaptado da vinicultura onde frutos inteiros fermentam em ambiente rico em CO2. A fermentação intracelular cria características únicas, resultando em cafés com perfil sensorial distinto, frequentemente mais doces e com notas frutadas intensas.",
    tags: ["Processamento"]
  },
  {
    id: "barista",
    termo: "Barista",
    definicao: "Profissional especializado no preparo e apresentação de bebidas à base de café. Domina técnicas de extração, latte art, conhecimento sobre grãos, métodos de preparo e atendimento ao cliente. Responsável por garantir qualidade e consistência das bebidas servidas.",
    tags: ["Profissional"]
  },
  {
    id: "q-grader",
    termo: "Q-Grader",
    definicao: "Certificação profissional internacional para avaliadores de qualidade de café arábica, oferecida pelo Coffee Quality Institute (CQI). Q-Graders certificados possuem habilidades padronizadas para avaliar e pontuar cafés especiais, sendo fundamentais na cadeia de qualidade do café.",
    tags: ["Profissional"]
  },
  {
    id: "sca",
    termo: "SCA",
    definicao: "Specialty Coffee Association - organização sem fins lucrativos que representa milhares de profissionais do café mundialmente. Desenvolve padrões de qualidade, protocolos de degustação, programas educacionais e certificações, sendo a principal autoridade em cafés especiais.",
    tags: ["Profissional"]
  },
  {
    id: "terceira-onda",
    termo: "Terceira Onda",
    definicao: "Movimento cultural que trata o café como produto artesanal e de alta qualidade, similar ao vinho. Enfatiza origem, processamento, torra artesanal e métodos de preparo precisos. Valoriza a experiência sensorial completa e a conexão entre produtor e consumidor.",
    tags: ["Profissional"]
  },
  {
    id: "cafeina",
    termo: "Cafeína",
    definicao: "Alcaloide do grupo das xantinas, principal composto psicoativo do café. Atua como estimulante do sistema nervoso central, proporcionando estado de alerta e reduzindo fadiga. A concentração varia conforme espécie, torra e método de preparo, sendo maior no robusta que no arábica.",
    tags: ["Química"]
  },
  {
    id: "caramelizacao",
    termo: "Caramelização",
    definicao: "Reação química que ocorre durante a torra quando açúcares são aquecidos acima de 170°C, formando compostos caramelizados. Diferente da reação de Maillard, envolve apenas açúcares e é responsável por sabores doces, notas de caramelo e desenvolvimento da cor marrom do café torrado.",
    tags: ["Química", "Torra"]
  },
  {
    id: "ph",
    termo: "pH",
    definicao: "Medida que indica o grau de acidez ou alcalinidade do café, variando de 0 (muito ácido) a 14 (muito alcalino). O café geralmente apresenta pH entre 4,5 e 6,0, sendo influenciado por origem, processamento, torra e método de preparo. pH mais baixo indica maior acidez.",
    tags: ["Química"]
  },
  {
    id: "desgaseificacao",
    termo: "Desgaseificação",
    definicao: "Processo natural de liberação de CO2 formado durante a torra do café. Ocorre principalmente nas primeiras 24-48 horas após a torra, sendo essencial para estabilizar o café antes do consumo. A desgaseificação adequada melhora a extração e evita sabores indesejados.",
    tags: ["Torra"]
  },
  {
    id: "primeiro-crack",
    termo: "Primeiro Crack",
    definicao: "Marco audível durante a torra, caracterizado por sons de estalo quando a estrutura celular dos grãos se rompe devido à expansão de vapor. Ocorre entre 196-205°C, indicando que o café atingiu nível mínimo de torra e desenvolveu sabores básicos.",
    tags: ["Torra"]
  },
  {
    id: "segundo-crack",
    termo: "Segundo Crack",
    definicao: "Segunda série de estalos durante a torra, mais suave que o primeiro crack, ocorrendo entre 224-230°C. Indica início da torra escura, quando óleos começam a migrar para superfície e características de origem são gradualmente mascaradas por sabores de torra.",
    tags: ["Torra"]
  },
  {
    id: "torra-clara",
    termo: "Torra Clara",
    definicao: "Nível de torra logo após o primeiro crack, preservando máxima acidez e características originais do grão. Resulta em café com corpo leve, acidez brilhante e perfil sensorial complexo, ideal para destacar nuances de terroir e processamento.",
    tags: ["Torra"]
  },
  {
    id: "torra-escura",
    termo: "Torra Escura",
    definicao: "Nível de torra após o segundo crack, caracterizada por grãos oleosos e cor marrom escura. Desenvolve sabores intensos de caramelo, chocolate amargo e notas tostadas, com corpo encorpado e acidez reduzida. Características de origem são mascaradas pelos sabores de torra.",
    tags: ["Torra"]
  }
];

// Todas as tags disponíveis
const todasTags = Array.from(new Set(termosGlossario.flatMap(termo => termo.tags))).sort();

// Função para gerar variações de um termo (plural/singular)
function gerarVariacoes(termo: string): string[] {
  const variações = [termo];
  const termoLower = termo.toLowerCase();

  // Para termos compostos, também adicionar versões com diferentes casos
  if (termo.includes(' ')) {
    variações.push(termo.toLowerCase());
    variações.push(termo.toUpperCase());
    // Adicionar versão title case
    variações.push(termo.split(' ').map(palavra =>
      palavra.charAt(0).toUpperCase() + palavra.slice(1).toLowerCase()
    ).join(' '));
  }

  // Regras básicas de plural/singular em português
  if (termoLower.endsWith('s') && termoLower.length > 3) {
    // Remover 's' final para singular
    const singular = termo.slice(0, -1);
    variações.push(singular);
    if (singular.includes(' ')) {
      variações.push(singular.toLowerCase());
    }
  } else {
    // Adicionar 's' para plural
    const plural = termo + 's';
    variações.push(plural);
    if (plural.includes(' ')) {
      variações.push(plural.toLowerCase());
    }
  }

  // Variações específicas comuns
  if (termoLower.endsWith('ão')) {
    const plural = termo.slice(0, -2) + 'ões';
    variações.push(plural);
    if (plural.includes(' ')) {
      variações.push(plural.toLowerCase());
    }
  }
  if (termoLower.endsWith('ões')) {
    const singular = termo.slice(0, -3) + 'ão';
    variações.push(singular);
    if (singular.includes(' ')) {
      variações.push(singular.toLowerCase());
    }
  }

  return [...new Set(variações)];
}

// Função para detectar e linkar termos nas definições
function processarDefinicaoComLinks(
  definicao: string,
  termoAtual: string,
  todosTermos: TermoGlossario[],
  onTermoClick: (termoId: string) => void
): React.ReactNode {
  // Filtrar termos que não são o atual
  const termosParaLinkar = todosTermos.filter(termo => termo.termo !== termoAtual);

  // Criar lista de todos os termos com suas variações
  const termosComVariacoes: Array<{
    termo: TermoGlossario;
    variacao: string;
    prioridade: number; // 0 = exato, 1 = variação
  }> = [];

  termosParaLinkar.forEach(termo => {
    const variações = gerarVariacoes(termo.termo);
    variações.forEach((variacao, index) => {
      termosComVariacoes.push({
        termo,
        variacao,
        prioridade: index === 0 ? 0 : 1 // Primeira variação (original) tem prioridade
      });
    });
  });

  // Ordenar por comprimento (maior primeiro) e depois por prioridade
  termosComVariacoes.sort((a, b) => {
    if (a.variacao.length !== b.variacao.length) {
      return b.variacao.length - a.variacao.length;
    }
    return a.prioridade - b.prioridade;
  });

  const matches: Array<{
    termo: TermoGlossario;
    inicio: number;
    fim: number;
    textoOriginal: string;
  }> = [];

  // Encontrar todos os matches usando busca simples
  const termosJaLinkados = new Set<string>(); // Controlar termos já linkados

  termosComVariacoes.forEach(({ termo, variacao }) => {
    // Se o termo já foi linkado, pular
    if (termosJaLinkados.has(termo.id)) {
      return;
    }

    // Usar indexOf para busca mais simples e confiável
    const variacaoLower = variacao.toLowerCase();
    const definicaoLower = definicao.toLowerCase();

    let startIndex = 0;
    let index;

    while ((index = definicaoLower.indexOf(variacaoLower, startIndex)) !== -1) {
      // Verificar se é uma palavra completa (word boundaries)
      const charAntes = index > 0 ? definicaoLower[index - 1] : ' ';
      const charDepois = index + variacaoLower.length < definicaoLower.length
        ? definicaoLower[index + variacaoLower.length]
        : ' ';

      const isWordBoundaryAntes = /\W/.test(charAntes);
      const isWordBoundaryDepois = /\W/.test(charDepois);

      if (isWordBoundaryAntes && isWordBoundaryDepois) {
        const novoMatch = {
          termo,
          inicio: index,
          fim: index + variacao.length,
          textoOriginal: definicao.slice(index, index + variacao.length)
        };

        // Verificar se não sobrepõe com matches existentes
        const sobrepoe = matches.some(m =>
          (novoMatch.inicio >= m.inicio && novoMatch.inicio < m.fim) ||
          (novoMatch.fim > m.inicio && novoMatch.fim <= m.fim) ||
          (novoMatch.inicio <= m.inicio && novoMatch.fim >= m.fim)
        );

        if (!sobrepoe) {
          matches.push(novoMatch);
          termosJaLinkados.add(termo.id); // Marcar termo como já linkado
          break; // Sair do loop para não linkar mais ocorrências do mesmo termo
        }
      }

      startIndex = index + 1;
    }
  });

  // Ordenar matches por posição
  matches.sort((a, b) => a.inicio - b.inicio);

  // Se não há matches, retornar texto original
  if (matches.length === 0) {
    return definicao;
  }



  // Construir JSX com links
  const elementos: React.ReactNode[] = [];
  let ultimaPosicao = 0;

  matches.forEach((match, index) => {
    // Adicionar texto antes do match
    if (match.inicio > ultimaPosicao) {
      elementos.push(
        <span key={`text-before-${index}`}>
          {definicao.slice(ultimaPosicao, match.inicio)}
        </span>
      );
    }

    // Adicionar link
    elementos.push(
      <button
        key={`link-${index}`}
        type="button"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onTermoClick(match.termo.id);
        }}
        className="font-bold text-primary hover:text-primary/80 transition-colors cursor-pointer underline-offset-2 hover:underline"
        title={`Ir para: ${match.termo.termo}`}
      >
        {match.textoOriginal}
      </button>
    );

    ultimaPosicao = match.fim;
  });

  // Adicionar texto restante
  if (ultimaPosicao < definicao.length) {
    elementos.push(
      <span key="text-final">
        {definicao.slice(ultimaPosicao)}
      </span>
    );
  }

  return <>{elementos}</>;
}

// Componente para cada termo com auto-linkagem
function TermoItem({
  termo,
  todosTermos,
  onTermoClick,
  isHighlighted
}: {
  termo: TermoGlossario;
  todosTermos: TermoGlossario[];
  onTermoClick: (termoId: string) => void;
  isHighlighted?: boolean;
}) {
  const termoRef = useRef<HTMLDivElement>(null);

  // Efeito para destacar temporariamente quando navegado
  useEffect(() => {
    if (isHighlighted && termoRef.current) {
      termoRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });

      // Remover destaque após animação
      const timer = setTimeout(() => {
        // O destaque será removido pelo componente pai
      }, 4000);

      return () => clearTimeout(timer);
    }
  }, [isHighlighted]);

  const definicaoComLinks = processarDefinicaoComLinks(
    termo.definicao,
    termo.termo,
    todosTermos,
    onTermoClick
  );

  return (
    <div
      ref={termoRef}
      id={`termo-${termo.id}`}
      className={`bg-card p-4 sm:p-6 rounded-lg border shadow-sm hover:shadow-md transition-all duration-300 border-border ${
        isHighlighted ? 'ring-2 ring-primary ring-opacity-50 bg-primary/5' : ''
      }`}
    >
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
        <div className="flex-1">
          <h3 className="text-lg sm:text-xl font-semibold text-foreground mb-2">
            {termo.termo}
          </h3>
          <div className="text-sm sm:text-base text-muted-foreground leading-relaxed">
            {definicaoComLinks}
          </div>
        </div>
      </div>

      {/* Tags */}
      <div className="flex flex-wrap gap-2 mt-4">
        {termo.tags.map(tag => (
          <span
            key={tag}
            className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-md"
          >
            <Tag size={12} />
            {tag}
          </span>
        ))}
      </div>
    </div>
  );
}

export default function Glossario() {
  const [busca, setBusca] = useState("");
  const [tagSelecionada, setTagSelecionada] = useState<string | null>(null);
  const [sidebarAberta, setSidebarAberta] = useState<boolean>(false);
  const [sidebarFechando, setSidebarFechando] = useState<boolean>(false);

  // Alterar título da página
  useEffect(() => {
    document.title = "Cereja - Glossário";
  }, []);
  const [termosVisiveis, setTermosVisiveis] = useState(20);
  const [termoDestacado, setTermoDestacado] = useState<string | null>(null);
  const [mostrarBotaoTopo, setMostrarBotaoTopo] = useState<boolean>(false);
  const [mostrarBotaoTopoDesktop, setMostrarBotaoTopoDesktop] = useState<boolean>(false);
  const sidebarRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // Função para fechar sidebar com animação
  const fecharSidebar = useCallback(() => {
    setSidebarFechando(true);
    setTimeout(() => {
      setSidebarAberta(false);
      setSidebarFechando(false);
    }, 300);
  }, []);

  // Fechar sidebar ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        fecharSidebar();
      }
    };

    if (sidebarAberta) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [sidebarAberta, fecharSidebar]);

  // Detectar scroll para mostrar/ocultar botões "Voltar ao topo"
  useEffect(() => {
    const handleScroll = () => {
      const isMobile = window.innerWidth < 768;
      const scrollY = window.scrollY;

      // Botão mobile: mostrar apenas em mobile quando rolar mais de 300px
      setMostrarBotaoTopo(isMobile && scrollY > 300);

      // Botão desktop: mostrar apenas em desktop quando rolar mais de 400px
      setMostrarBotaoTopoDesktop(!isMobile && scrollY > 400);
    };

    // Adicionar listener de scroll
    window.addEventListener('scroll', handleScroll);
    // Verificar também no resize para detectar mudanças de dispositivo
    window.addEventListener('resize', handleScroll);

    // Verificar estado inicial
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, []);

  // Cache para filtros de busca
  const cacheFiltros = useRef(new Map<string, TermoGlossario[]>());

  // Debounce para busca
  const [buscaDebounced, setBuscaDebounced] = useState(busca);

  useEffect(() => {
    const timer = setTimeout(() => {
      setBuscaDebounced(busca);
      // Tracking da busca (apenas se não estiver vazia)
      if (busca.trim()) {
        trackGlossaryFilter('search', busca.trim());
      }
    }, 150); // 150ms de debounce

    return () => clearTimeout(timer);
  }, [busca]);

  // Filtrar termos baseado na busca e tag selecionada - otimizado com cache e debounce
  const termosFiltrados = useMemo(() => {
    const chaveCache = `${buscaDebounced}|${tagSelecionada || ''}`;

    // Verificar cache primeiro
    if (cacheFiltros.current.has(chaveCache)) {
      return cacheFiltros.current.get(chaveCache)!;
    }

    const termosFiltradosBase = termosGlossario
      .filter(termo => {
        const correspondeTexto = !buscaDebounced.trim() ||
          termo.termo.toLowerCase().includes(buscaDebounced.toLowerCase()) ||
          termo.definicao.toLowerCase().includes(buscaDebounced.toLowerCase());
        const correspondeTag = !tagSelecionada || termo.tags.includes(tagSelecionada);

        return correspondeTexto && correspondeTag;
      });

    let resultado: TermoGlossario[];

    // Se não há busca, ordenar alfabeticamente
    if (!buscaDebounced.trim()) {
      resultado = termosFiltradosBase.sort((a, b) =>
        a.termo.localeCompare(b.termo, 'pt-BR', { sensitivity: 'base' })
      );
    } else {
      // Com busca ativa, aplicar priorização inteligente
      const buscaNormalizada = buscaDebounced.toLowerCase().trim();

      resultado = termosFiltradosBase.sort((a, b) => {
        const termoA = a.termo.toLowerCase();
        const termoB = b.termo.toLowerCase();
        const definicaoA = a.definicao.toLowerCase();
        const definicaoB = b.definicao.toLowerCase();

        // Prioridade 1: Correspondência exata do termo
        const exactMatchA = termoA === buscaNormalizada;
        const exactMatchB = termoB === buscaNormalizada;
        if (exactMatchA && !exactMatchB) return -1;
        if (!exactMatchA && exactMatchB) return 1;

        // Prioridade 2: Termo começa com a busca
        const startsWithA = termoA.startsWith(buscaNormalizada);
        const startsWithB = termoB.startsWith(buscaNormalizada);
        if (startsWithA && !startsWithB) return -1;
        if (!startsWithA && startsWithB) return 1;

        // Prioridade 3: Termo contém a busca
        const containsInTermoA = termoA.includes(buscaNormalizada);
        const containsInTermoB = termoB.includes(buscaNormalizada);
        if (containsInTermoA && !containsInTermoB) return -1;
        if (!containsInTermoA && containsInTermoB) return 1;

        // Prioridade 4: Definição contém a busca (menor prioridade)
        const containsInDefA = definicaoA.includes(buscaNormalizada);
        const containsInDefB = definicaoB.includes(buscaNormalizada);
        if (containsInDefA && !containsInDefB) return -1;
        if (!containsInDefA && containsInDefB) return 1;

        // Prioridade 5: Ordem alfabética para casos iguais
        return a.termo.localeCompare(b.termo, 'pt-BR', { sensitivity: 'base' });
      });
    }

    // Armazenar no cache (limitar tamanho do cache)
    if (cacheFiltros.current.size > 50) {
      cacheFiltros.current.clear();
    }
    cacheFiltros.current.set(chaveCache, resultado);

    return resultado;
  }, [buscaDebounced, tagSelecionada]);

  // Resetar termos visíveis quando filtros mudam
  useEffect(() => {
    setTermosVisiveis(20);
  }, [buscaDebounced, tagSelecionada]);

  // Intersection Observer para carregamento progressivo
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && termosVisiveis < termosFiltrados.length) {
          setTermosVisiveis(prev => Math.min(prev + 20, termosFiltrados.length));
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [termosVisiveis, termosFiltrados.length]);

  // Termos para renderizar (limitados)
  const termosParaRenderizar = useMemo(() => {
    return termosFiltrados.slice(0, termosVisiveis);
  }, [termosFiltrados, termosVisiveis]);

  // Função para navegar para um termo específico
  const navegarParaTermo = (termoId: string) => {
    // Primeiro, verificar se o termo existe no glossário completo
    const termoExiste = termosGlossario.find(t => t.id === termoId);

    if (!termoExiste) {
      console.warn(`Termo com ID "${termoId}" não encontrado no glossário`);
      return;
    }

    // Tracking da visualização do termo
    trackGlossaryTermViewed(termoExiste.termo, termoId);
    trackGlossaryNavigation('term_click', { term_id: termoId, term_name: termoExiste.termo });

    // Verificar se o termo está nos termos filtrados atuais
    const termoEncontrado = termosFiltrados.find(t => t.id === termoId);

    if (termoEncontrado) {
      // Termo está nos filtros atuais
      const indiceDoTermo = termosFiltrados.findIndex(t => t.id === termoId);

      if (indiceDoTermo < termosVisiveis) {
        // Termo já está visível, apenas destacar
        setTermoDestacado(termoId);
        setTimeout(() => setTermoDestacado(null), 4000);
      } else {
        // Termo não está visível, expandir lista e destacar
        setTermosVisiveis(Math.max(termosVisiveis, indiceDoTermo + 1));
        setTimeout(() => {
          setTermoDestacado(termoId);
          setTimeout(() => setTermoDestacado(null), 4000);
        }, 100);
      }
    } else {
      // Termo não está nos filtros atuais - sempre limpar filtros quando o termo não está visível
      setBusca("");
      setTagSelecionada(null);

      // Aguardar filtros serem limpos e então navegar
      setTimeout(() => {
        // Recalcular posição no glossário completo ordenado alfabeticamente
        const termosOrdenados = [...termosGlossario].sort((a, b) =>
          a.termo.localeCompare(b.termo, 'pt-BR', { sensitivity: 'base' })
        );
        const indiceDoTermo = termosOrdenados.findIndex(t => t.id === termoId);

        if (indiceDoTermo >= 0) {
          // Garantir que o termo estará visível
          setTermosVisiveis(Math.max(20, indiceDoTermo + 5));

          // Aguardar um pouco mais para garantir que a lista foi atualizada
          setTimeout(() => {
            setTermoDestacado(termoId);
            setTimeout(() => setTermoDestacado(null), 4000);
          }, 300); // Aumentei o timeout para dar mais tempo para a atualização
        }
      }, 200); // Aumentei o timeout inicial também
    }
  };

  // Função para voltar ao topo da página
  const voltarAoTopo = () => {
    trackButtonClick('voltar_ao_topo', 'glossario');
    trackGlossaryNavigation('scroll_to_top');
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header com menu hambúrguer - fixo em ambos mobile e desktop */}
      <header className="fixed top-4 left-4 sm:top-6 sm:left-6 z-30">
        <button
          onClick={() => setSidebarAberta(true)}
          className="flex items-center gap-2 p-2 text-muted-foreground hover:text-primary hover:bg-accent/50 rounded-lg sm:rounded-lg rounded-full sm:bg-card/90 sm:backdrop-blur-sm sm:shadow-lg sm:border sm:border-border/20 bg-card/90 backdrop-blur-sm shadow-lg border border-border/20 transition-all"
        >
          <Menu size={20} />
          <span className="text-sm font-medium hidden sm:inline">Menu</span>
        </button>
      </header>

      {/* Sidebar */}
      {sidebarAberta && (
        <>
          {/* Overlay */}
          <div
            className={`fixed inset-0 bg-black/50 z-40 ${sidebarFechando ? 'animate-fade-out' : 'animate-fade-in'}`}
            onClick={fecharSidebar}
          />

          {/* Sidebar content */}
          <div
            ref={sidebarRef}
            className={`fixed top-0 left-0 h-full w-80 bg-card border-r border-border shadow-lg z-50 ${sidebarFechando ? 'animate-slide-out-left' : 'animate-slide-in-left'}`}
          >
            {/* Sidebar header */}
            <div className="flex items-center justify-between p-6 border-b border-border">
              <div className="flex items-center gap-3">
                <Image
                  src="/cereja.png"
                  alt="Cereja"
                  width={40}
                  height={40}
                  className="object-contain"
                  quality={75}
                />
                <div>
                  <h2 className="text-xl font-bold text-foreground">cereja</h2>
                  <p className="text-sm text-muted-foreground">sua calculadora pessoal ♥</p>
                </div>
              </div>
              <button
                onClick={fecharSidebar}
                className="p-2 hover:bg-accent rounded-lg transition-colors"
              >
                <X size={20} />
              </button>
            </div>

            {/* Navigation menu */}
            <nav className="p-6">
              <div className="space-y-2">
                <Link
                  href="/"
                  onClick={fecharSidebar}
                  className="flex items-center gap-3 px-4 py-3 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                >
                  <HomeIcon size={20} />
                  <span className="font-medium">Calculadora 4:6</span>
                </Link>



                <Link
                  href="/glossario"
                  onClick={fecharSidebar}
                  className="flex items-center gap-3 px-4 py-3 text-primary bg-primary/10 rounded-lg transition-colors"
                >
                  <BookOpen size={20} />
                  <span className="font-medium">Glossário</span>
                </Link>
              </div>

              {/* Botão de doação */}
              <div className="mt-6 pt-6 border-t border-border">
                <a
                  href="https://ko-fi.com/pedrogott"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={fecharSidebar}
                  className="w-full flex items-center gap-3 px-4 py-3 text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl border border-orange-400/30 transform hover:scale-[1.02]"
                >
                  <Coffee size={20} />
                  <div className="flex flex-col text-left">
                    <span className="font-medium text-sm">Gostou do projeto?</span>
                    <span className="text-xs opacity-80">Pague-me um café!</span>
                  </div>
                </a>
              </div>

              {/* Link de Política de Privacidade - discreto */}
              <div className="mt-4 pt-4 border-t border-border/50">
                <Link
                  href="/privacidade"
                  onClick={fecharSidebar}
                  className="block px-4 py-2 text-xs text-muted-foreground hover:text-muted-foreground/80 transition-colors text-center"
                >
                  Política de Privacidade
                </Link>
              </div>
            </nav>
          </div>
        </>
      )}

      {/* Header */}
      <div className="flex flex-col items-center justify-start pt-2 sm:pt-4 px-4 sm:px-8 gap-4 sm:gap-6">
        {/* Título grande centralizado */}
        <div className="flex items-center gap-2 sm:gap-3 mt-4 sm:mt-2 mb-6">
          <Image
            src="/cereja.png"
            alt="Cereja"
            width={80}
            height={80}
            priority
            className="object-contain sm:w-[100px] sm:h-[100px]"
            quality={90}
          />
          <div className="text-center">
            <h1 className="text-4xl sm:text-6xl font-bold text-foreground">
              cereja
            </h1>
            <p className="text-xs sm:text-sm text-muted-foreground mt-1 sm:mt-2">
              glossário de café
            </p>
          </div>
        </div>

        {/* Barra de pesquisa */}
        <div className="w-full max-w-4xl">
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={18} />
            <input
              type="text"
              placeholder="Pesquisar termos..."
              value={busca}
              onChange={(e) => setBusca(e.target.value)}
              className="w-full pl-10 pr-4 py-3 text-base bg-input border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* Filtros por tag */}
          <div className="flex flex-wrap gap-2 mb-6">
            <button
              onClick={() => setTagSelecionada(null)}
              className={`flex items-center gap-1 px-3 py-2 rounded-full text-xs font-medium transition-colors ${
                !tagSelecionada
                  ? "bg-primary text-primary-foreground"
                  : "bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground"
              }`}
            >
              <Tag size={14} />
              Todas
            </button>
            {todasTags.map(tag => (
              <button
                key={tag}
                onClick={() => {
                  const novaTag = tag === tagSelecionada ? null : tag;
                  setTagSelecionada(novaTag);
                  // Tracking da seleção de tag
                  if (novaTag) {
                    trackGlossaryFilter('tag', novaTag);
                    trackButtonClick('selecionar_tag', 'glossario');
                  } else {
                    trackButtonClick('desselecionar_tag', 'glossario');
                  }
                }}
                className={`px-3 py-2 rounded-full text-xs font-medium transition-colors ${
                  tagSelecionada === tag
                    ? "bg-primary text-primary-foreground"
                    : "bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground"
                }`}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>

        {/* Lista de termos */}
        <div className="w-full max-w-4xl flex-1 mb-8">
          {termosFiltrados.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground mb-6">
                Nenhum termo encontrado
                {busca && ` para "${busca}"`}
                {tagSelecionada && ` na categoria "${tagSelecionada}"`}
              </p>

              {/* Botões de limpeza */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
                {busca && (
                  <button
                    onClick={() => {
                      setBusca("");
                      trackButtonClick('limpar_busca', 'glossario');
                    }}
                    className="flex items-center gap-2 px-4 py-2 bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground rounded-lg transition-colors"
                  >
                    <X size={16} />
                    Limpar Pesquisa
                  </button>
                )}

                {tagSelecionada && (
                  <button
                    onClick={() => {
                      setTagSelecionada(null);
                      trackButtonClick('limpar_tag', 'glossario');
                    }}
                    className="flex items-center gap-2 px-4 py-2 bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground rounded-lg transition-colors"
                  >
                    <X size={16} />
                    Limpar Filtro
                  </button>
                )}

                {(busca || tagSelecionada) && (
                  <button
                    onClick={() => {
                      setBusca("");
                      setTagSelecionada(null);
                      trackButtonClick('limpar_tudo', 'glossario');
                    }}
                    className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg transition-colors"
                  >
                    <X size={16} />
                    Limpar Tudo
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="grid gap-4">
              {termosParaRenderizar.map(termo => (
                <TermoItem
                  key={termo.id}
                  termo={termo}
                  todosTermos={termosGlossario}
                  onTermoClick={navegarParaTermo}
                  isHighlighted={termoDestacado === termo.id}
                />
              ))}

              {/* Elemento para carregamento progressivo */}
              {termosVisiveis < termosFiltrados.length && (
                <div
                  ref={loadMoreRef}
                  className="flex items-center justify-center py-8"
                >
                  <div className="text-sm text-muted-foreground">
                    Carregando mais termos... ({termosVisiveis} de {termosFiltrados.length})
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Botão Voltar ao Topo - mobile */}
      {mostrarBotaoTopo && (
        <button
          onClick={voltarAoTopo}
          className="fixed bottom-6 right-6 z-40 md:hidden flex items-center justify-center w-12 h-12 bg-primary text-primary-foreground rounded-full shadow-lg hover:bg-primary/90 hover:shadow-xl active:scale-95 transition-all duration-300 animate-slide-up-fade-in"
          aria-label="Voltar ao topo"
        >
          <ChevronUp size={20} />
        </button>
      )}

      {/* Botão Voltar ao Topo - desktop */}
      {mostrarBotaoTopoDesktop && (
        <button
          onClick={voltarAoTopo}
          className="fixed bottom-8 right-8 z-40 hidden md:flex items-center justify-center w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-lg hover:bg-primary/90 hover:shadow-xl active:scale-95 transition-all duration-300 animate-slide-up-fade-in"
          aria-label="Voltar ao topo"
        >
          <ChevronUp size={24} />
        </button>
      )}

      {/* Footer */}
      <footer className="bg-secondary/50 py-4 w-full text-center mt-auto">
        <p className="text-xs text-muted-foreground">
          Desenvolvido por{" "}
          <a
            href="https://ko-fi.com/pedrogott"
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:text-primary/80 transition-colors font-medium"
          >
            Pedro Gottardi
          </a>
        </p>
      </footer>
    </div>
  );
}
